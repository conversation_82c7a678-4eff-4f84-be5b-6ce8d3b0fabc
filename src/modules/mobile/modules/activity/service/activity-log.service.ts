import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import { Brackets, DataSource, Repository } from 'typeorm';
import { <PERSON><PERSON><PERSON>ead<PERSON> } from '../../../../../common/decorators/current-device.decorator';
import { Activity } from '../../../../../common/modules/database/entities/activity.entity';
import { Device } from '../../../../../common/modules/database/entities/device.entity';
import { LogActivity } from '../../../../../common/modules/database/entities/log-activity.entity';
import { LogAlert } from '../../../../../common/modules/database/entities/log-alert.entity';
import { Role } from '../../../../../common/modules/database/entities/role.entity';
import { User } from '../../../../../common/modules/database/entities/user.entity';
import { StorageService } from '../../../../../common/modules/storage/services/storage.service';
import { ApplyActivityDto } from '../dto/apply-activity.dto';
import { v4 as uuidv4 } from 'uuid';
import { Branch } from '../../../../../common/modules/database/entities/branch.entity';
import { Alert } from '../../../../../common/modules/database/entities/alert.entity';
import { UserBranch } from '../../../../../common/modules/database/entities/user-branch.entity';
import { Checkpoint } from '../../../../../common/modules/database/entities/checkpoint.entity';
import { LogCheckpoint } from '../../../../../common/modules/database/entities/log-checkpoint.entity';

@Injectable()
export class ActivityLogService {
  constructor(
    @InjectRepository(Activity)
    private activityRepository: Repository<Activity>,
    @InjectRepository(Role)
    private role: Repository<Role>,
    @InjectRepository(Device)
    private deviceRepository: Repository<Device>,
    @InjectRepository(Alert)
    private alertRepository: Repository<Alert>,
    @InjectRepository(UserBranch)
    private userBranchRepository: Repository<UserBranch>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Checkpoint)
    private checkpointRepository: Repository<Checkpoint>,
    private storageService: StorageService,
    private dataSource: DataSource,
  ) {}

  async apply(
    body: ApplyActivityDto,
    user: User,
    idActivity: number,
    file: Express.Multer.File,
    deviceFromHeader: DeviceHeader,
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      let device: Device | null = null;
      // Validate Device
      if (deviceFromHeader.device_uid) {
        const getDevice = await this.deviceRepository.findOne({
          where: {
            imei: deviceFromHeader.device_uid,
          },
        });
        if (!getDevice) {
          throw new NotFoundException('Device not found');
        }
        device = getDevice;
      }

      // Validate activity exists
      const activity = await this.activityRepository.findOne({
        where: { id: idActivity },
        relations: [
          'branch',
          'parent_branch',
          'parent_branch',
          'branch.timezone',
        ],
      });
      if (!activity) {
        throw new NotFoundException('Activity not found');
      }

      // Validate role
      const role = await this.role.findOne({
        where: { id: user.role_id, parent_branch_id: user.parent_branch_id },
      });
      if (!role) {
        throw new NotFoundException('Role not found');
      }

      // Validate requirements
      this.validateActivityRequirements(activity, body, file);

      // Upload photo if exists
      const photoUrl = await this.uploadPhoto(file);

      // Create and save log activity
      const logActivity = this.createLogActivity(
        activity,
        body,
        user,
        role,
        photoUrl,
        device,
      );
      const savedLogActivity = await queryRunner.manager.save(
        LogActivity,
        logActivity,
      );

      // Create and save log alert
      const logAlert = this.createLogAlert(activity, savedLogActivity, user);
      await queryRunner.manager.save(LogAlert, logAlert);

      await queryRunner.commitTransaction();

      return {
        message: 'Log activity created successfully',
        data: savedLogActivity,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async sendAlert(logAlert: LogCheckpoint, branch: Branch, user: User) {
    // Log Checkpoint - user
    const getUser = await this.userRepository.findOne({
      where: { id: user.id },
    });

    // Log Checkpoint - day of month start form 1
    const dayOfMonth = dayjs(logAlert.original_submitted_time).date();

    // Log Checkpoint - day of week start from 0
    const dayOfWeek = dayjs(logAlert.original_submitted_time).day();

    // Log Checkpoint - hours 0 to 23
    const hours = dayjs(logAlert.original_submitted_time).hour();

    // Log Checkpoint - minutes 0 to 59
    const minutes = dayjs(logAlert.original_submitted_time).minute();

    // Log Checkpoint - checkpoint
    const getCheckpoint = await this.checkpointRepository.findOne({
      where: { id: logAlert.checkpoint_id },
    });

    // Log Checkpoint - device
    const getDevice = await this.deviceRepository.findOne({
      where: { id: logAlert.device_id },
    });

    const userBranches = await this.userBranchRepository.find({
      where: {
        user_id: user.id,
        active: true,
      },
    });
    const branchIds = userBranches.map(userBranch => userBranch.branch_id);

    // Create query builder to Find the alert from Alert entity
    const alertQuery = this.alertRepository
      .createQueryBuilder('alert')
      .innerJoin('alert_branches', 'ab', 'ab.alert_id = alert.id')
      .where('alert.alert_action_id = :alertActionId', {
        alertActionId: 1,
      })
      .andWhere('alert.active = :active', { active: true })
      .andWhere('parent_branch_id = :parentBranchId', {
        parentBranchId: branch.parent_id,
      })
      .andWhere('ab.branch_id IN (:...branchIds)', { branchIds })
      .leftJoinAndSelect('alert.conditions', 'conditions');

    // Apply Alert OR Conditions
    const orConditions = alertQuery
      .clone()
      .andWhere(
        'alert.alert_logical_condition_type = :alertLogicalConditionType',
        {
          alertLogicalConditionType: 'OR',
        },
      )
      .andWhere(
        new Brackets(qb => {
          // User
          qb.orWhere(
            new Brackets(qb => {
              qb.where(
                'conditions.alert_condition_type_id = :alertConditionTypeId AND conditions.alert_operator_condition_type = :alertOperatorConditionType AND conditions.alert_condition_value_id = :userId',
                {
                  alertConditionTypeId: 1,
                  alertOperatorConditionType: '=',
                  userId: user.id,
                },
              ).orWhere(
                'conditions.alert_condition_type_id = :alertConditionTypeId AND conditions.alert_operator_condition_type = :alertOperatorConditionType AND conditions.alert_condition_value_id = :userId',
                {
                  alertConditionTypeId: 1,
                  alertOperatorConditionType: '=',
                  userId: user.id,
                },
              );
            }),
          );

          // Role
          qb.orWhere('conditions.role_id = :roleId', { roleId: user.role_id });

          // Day of month
          qb.orWhere(
            'conditions.alert_condition_type_id = :alertConditionTypeId AND conditions.alert_operator_condition_type = :alertOperatorConditionType AND conditions.alert_condition_value_id = :alertConditionValueId',
            {
              alertConditionTypeId: 1,
              alertOperatorConditionType: 1,
              alertConditionValueId: dayOfMonth,
            },
          );

          // Day of week
          qb.orWhere(
            'conditions.alert_condition_type_id = :alertConditionTypeId AND conditions.alert_operator_condition_type = :alertOperatorConditionType AND conditions.alert_condition_value_id = :alertConditionValueId',
            {
              alertConditionTypeId: 2,
              alertOperatorConditionType: 1,
              alertConditionValueId: dayOfWeek,
            },
          );

          // Hours
          qb.orWhere(
            'conditions.alert_condition_type_id = :alertConditionTypeId AND conditions.alert_operator_condition_type = :alertOperatorConditionType AND conditions.alert_condition_value_id = :alertConditionValueId',
            {
              alertConditionTypeId: 3,
              alertOperatorConditionType: 1,
              alertConditionValueId: hours,
            },
          );

          // Minutes
          qb.orWhere(
            'conditions.alert_condition_type_id = :alertConditionTypeId AND conditions.alert_operator_condition_type = :alertOperatorConditionType AND conditions.alert_condition_value_id = :alertConditionValueId',
            {
              alertConditionTypeId: 4,
              alertOperatorConditionType: 1,
              alertConditionValueId: minutes,
            },
          );
        }),
      );

    if (!alertQuery) {
      return;
    }
  }

  private validateActivityRequirements(
    activity: Activity,
    body: ApplyActivityDto,
    file?: Express.Multer.File,
  ) {
    if (activity.gps_required && (!body.latitude || !body.longitude)) {
      throw new BadRequestException('Latitude and longitude are required');
    }
    if (activity.photo_required && !file) {
      throw new BadRequestException('Photo is required');
    }
    if (activity.comment_required && !body.comment) {
      throw new BadRequestException('Comment is required');
    }
  }

  private async uploadPhoto(file?: Express.Multer.File): Promise<string> {
    if (!file) return '';

    const fileUuid = uuidv4();
    const fileExtension = file.originalname.split('.').pop();
    return await this.storageService.uploadFile(
      file,
      `log-activity/${fileUuid}.${fileExtension}`,
    );
  }

  private createLogActivity(
    activity: Activity,
    body: ApplyActivityDto,
    user: User,
    role: Role,
    photoUrl: string,
    device: Device | null,
  ): LogActivity {
    const logActivity = new LogActivity();

    logActivity.uuid = uuidv4();
    logActivity.parent_branch_id = user.parent_branch_id;
    logActivity.branch_id = activity.branch_id;
    logActivity.branch_name = activity.branch.branch_name;
    logActivity.activity_id = activity.id;
    logActivity.activity_name = activity.activity_name;
    logActivity.role_id = user.role_id;
    logActivity.role_name = role.role_name;
    logActivity.timezone_id = activity.branch.timezone.id;
    logActivity.timezone_name = activity.branch.timezone.timezone_name;
    logActivity.user_id = user.id;
    logActivity.user_name = user.name;

    if (body.latitude && body.longitude) {
      logActivity.latitude = body.latitude;
      logActivity.longitude = body.longitude;
    }

    if (photoUrl) {
      logActivity.photo_url = photoUrl;
      logActivity.photo_thumbnail_url = photoUrl;
    }

    if (body.comment) {
      logActivity.comment = body.comment;
    }

    if (device) {
      logActivity.device_id = device.id;
      logActivity.device_name = device.device_name;
    }

    logActivity.original_submitted_time = dayjs(
      body.original_submitted_time,
    ).toDate();
    logActivity.event_time = new Date();

    return logActivity;
  }

  private createLogAlert(
    activity: Activity,
    logActivity: LogActivity,
    user: User,
  ): LogAlert {
    const logAlert = new LogAlert();

    logAlert.uuid = uuidv4();
    logAlert.alert_event_id = 1;
    logAlert.alert_event_name = 'Activity Submitted';
    logAlert.log_id = logActivity.id;
    logAlert.log_uuid = logActivity.uuid;
    logAlert.reference_name = activity.activity_name;
    logAlert.parent_branch_id = user.parent_branch_id;
    logAlert.branch_id = activity.id;
    logAlert.user_id = user.id;
    logAlert.role_id = user.role_id;
    logAlert.payload_data = {
      type: 'activity',
      activity: activity,
      logActivity: logActivity,
    };
    logAlert.event_time = logActivity.event_time;
    logAlert.deleted_on_dashboard = false;
    logAlert.original_submitted_time = logActivity.original_submitted_time;

    return logAlert;
  }
}
