import { Transform, Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { EConditionType } from '../../../../../common/modules/database/entities/alert-condition-type.entity';
import { AlertOperatorConditionType } from '../../../../../common/modules/database/entities/alert-condition.entity';
import { RecipientType } from '../../../../../common/modules/database/entities/alert-recipient.entity';
import { AlertLogicalConditionType } from '../../../../../common/modules/database/entities/alert.entity';

// Define enum for alert actions based on database values
export enum AlertActionId {
  EMAIL = 1,
  SMS = 2,
}

export class CreateAlertConditionDto {
  @IsNumber({}, { message: 'Please select a valid condition type' })
  @IsNotEmpty({ message: 'Condition type is required' })
  alert_condition_type_id: EConditionType;

  @IsEnum(AlertOperatorConditionType, {
    message: 'Please select a valid operator',
  })
  @IsNotEmpty({ message: 'Operator is required' })
  alert_operator_condition_type: AlertOperatorConditionType;

  @IsNumber({}, { message: 'Please select a valid condition value' })
  @IsNotEmpty({ message: 'Condition value is required' })
  alert_condition_value_id: number;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.trim())
  alert_condition_description?: string;

  @IsOptional()
  @IsBoolean()
  active?: boolean = true;
}

export class CreateAlertRecipientDto {
  @IsEnum(RecipientType, { message: 'Please select a valid recipient type' })
  @IsNotEmpty({ message: 'Recipient type is required' })
  recipient_type: RecipientType;

  @ValidateIf(o => o.recipient_type === RecipientType.EMAIL)
  @IsEmail({}, { message: 'Please enter a valid email address' })
  @ValidateIf(o => o.recipient_type === RecipientType.PHONE)
  @Matches(/^\+[1-9]\d{1,14}$/, {
    message:
      'Please enter a valid phone number in international format (e.g., +1234567890)',
  })
  @IsNotEmpty({ message: 'Recipient contact is required' })
  recipient_contact: string;
}

export class CreateAlertDto {
  @IsString()
  @IsNotEmpty({ message: 'Alert name is required' })
  @Transform(({ value }) => value?.trim())
  alert_name: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => value?.trim())
  alert_description?: string;

  @IsNumber({}, { message: 'Please select a valid alert action' })
  @IsNotEmpty({ message: 'Alert action is required' })
  alert_action_id: AlertActionId;

  @IsNumber()
  @IsOptional()
  alert_event_id?: number;

  @IsEnum(AlertLogicalConditionType, {
    message: 'Please select a valid logical condition',
  })
  @IsNotEmpty({ message: 'Logical condition is required' })
  alert_logical_condition_type: AlertLogicalConditionType;

  @ValidateIf(o => o.alert_action_id === AlertActionId.EMAIL)
  @IsString()
  @IsNotEmpty({ message: 'Subject is required for email alerts' })
  @Transform(({ value }) => value?.trim())
  subject?: string;

  @ValidateIf(o => o.alert_action_id === AlertActionId.EMAIL)
  @IsString()
  @IsNotEmpty({ message: 'Message is required for email alerts' })
  @Transform(({ value }) => value?.trim())
  message?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateAlertConditionDto)
  conditions: CreateAlertConditionDto[];

  @ValidateIf(o =>
    [AlertActionId.EMAIL, AlertActionId.SMS].includes(o.alert_action_id),
  )
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1, {
    message: 'At least one recipient is required for email or SMS alerts',
  })
  @Type(() => CreateAlertRecipientDto)
  recipients?: CreateAlertRecipientDto[];

  @IsArray()
  @ArrayMinSize(1, { message: 'At least one branch must be selected' })
  @IsNumber({}, { each: true, message: 'Each branch ID must be a number' })
  branch_ids: number[];

  @IsOptional()
  @IsBoolean()
  active: boolean = true;
}
