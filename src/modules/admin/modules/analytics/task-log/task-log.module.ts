import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TaskLogController } from './controllers/task-log.controller';
import { TaskLogService } from './services/task-log.service';
import { StorageModule } from '../../../../../common/modules/storage/storage.module';
import { TaskAnalyticModule } from '../../../../../common/modules/analytics/task/task-analytic.module';

@Module({
  imports: [StorageModule, TaskAnalyticModule],
  controllers: [TaskLogController],
  providers: [TaskLogService],
  exports: [],
})
export class TaskLogModule {}
