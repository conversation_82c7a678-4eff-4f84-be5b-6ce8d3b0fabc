import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TimeOnZoneController } from './controllers/time-on-zone.controller';
import { TimeOnZoneService } from './services/time-on-zone.service';
import { StorageModule } from '../../../../../common/modules/storage/storage.module';
import { TimeOnZoneAnalyticModule } from '../../../../../common/modules/analytics/time-on-zone/time-on-zone-analytic.module';

@Module({
  imports: [StorageModule, TimeOnZoneAnalyticModule],
  controllers: [TimeOnZoneController],
  providers: [TimeOnZoneService],
  exports: [],
})
export class TimeOnZoneModule {}
