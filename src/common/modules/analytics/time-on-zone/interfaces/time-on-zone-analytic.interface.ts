import { OrderDirection } from '../../../../dto/base-query.dto';

export interface TimeOnZoneAnalyticParams {
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  branchId?: number | null;
  zoneId?: number | null;
  zoneLabels?: number[] | string[] | null;
  userId?: number | null;
  userLabels?: number[] | string[] | null;
  deviceId?: number | null;
  deviceLabels?: number[] | string[] | null;
  page?: number;
  limit?: number;
  orderBy?: string;
  orderDirection?: OrderDirection;
}

export interface TimeOnZoneData {
  zone_id: number;
  zone_name: string;
  user_id: number;
  user_name: string;
  device_id: number;
  device_name: string;
  branch_id: number;
  branch_name: string;
  branch_code: string;
  duration: number; // in seconds
  latitude: number;
  longitude: number;
  timezone_id: number;
  timezone_name: string;
  original_submitted_time: Date;
  entry: Date;
  exit: Date;
}
