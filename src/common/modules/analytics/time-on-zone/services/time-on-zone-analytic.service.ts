import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository } from 'typeorm';
import { LogCheckpoint } from '../../../database/entities/log-checkpoint.entity';
import { Timezone } from '../../../database/entities/timezone.entity';
import { User } from '../../../database/entities/user.entity';
import { Branch } from '../../../database/entities/branch.entity';
import { OrderDirection } from '../../../../dto/base-query.dto';
import { parseTimeDaily } from '../../../../utils/time-utils';
import {
  TimeOnZoneAnalyticParams,
  TimeOnZoneData,
} from '../interfaces/time-on-zone-analytic.interface';
import * as dayjs from 'dayjs';

@Injectable()
export class TimeOnZoneAnalyticService {
  constructor(
    @InjectRepository(LogCheckpoint)
    private readonly logCheckpointRepository: Repository<LogCheckpoint>,
    @InjectRepository(Branch)
    private readonly branchRepository: Repository<Branch>,
  ) {}

  /**
   * Retrieves time spent in zones with detailed analytics
   *
   * @param parent_branch_id Parent branch identifier
   * @param params Filter parameters including date range, branch, zone, user, device filters
   * @param options Additional context like timezone and user info
   * @returns Object with time on zone data and total count for pagination
   */
  async getTimeOnZone(
    parent_branch_id: string | number,
    params: TimeOnZoneAnalyticParams,
    options: {
      timezone?: Timezone;
      user?: User;
    },
  ) {
    // Extract query parameters with defaults
    const {
      startDate,
      endDate,
      startTime = '00:00:00',
      endTime = '23:59:59',
      branchId,
      zoneId,
      zoneLabels,
      userId,
      userLabels,
      deviceId,
      deviceLabels,
      page = 1,
      limit = 10,
      orderBy,
      orderDirection = OrderDirection.ASC,
    } = params;

    // Validate timezone
    const timezone = options.timezone;
    if (!timezone) {
      throw new NotFoundException('Timezone not found');
    }

    // Parse date/time filters with timezone offset
    const rangeDateAndTimeDaily = parseTimeDaily({
      startDate: startDate,
      endDate: endDate,
      startTime: startTime,
      endTime: endTime,
      offset: timezone.gmt_offset,
    });

    // Build subquery to extract log checkpoints with sequence numbers
    const subquery = this.logCheckpointRepository
      .createQueryBuilder('log_checkpoint')
      .select([
        'uuid',
        'zone_id',
        'zone_name',
        'user_id',
        'user_name',
        'device_id',
        'device_name',
        'branch_id',
        'latitude',
        'longitude',
        'original_submitted_time',
        // Calculate sequence difference to identify zone entry/exit pairs
        [
          '(ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY original_submitted_time ASC, uuid))',
          '-',
          '(ROW_NUMBER() OVER (PARTITION BY device_id, zone_id ORDER BY original_submitted_time ASC, uuid))',
          'as seqnum',
        ].join(' '),
      ])
      .where('log_checkpoint.parent_branch_id = :parent_branch_id', {
        parent_branch_id,
      })

      // Apply date range filters using brackets for multiple date ranges
      .andWhere(
        new Brackets(qb => {
          for (let i = 0; i < rangeDateAndTimeDaily.length; i++) {
            qb.orWhere(
              `log_checkpoint.original_submitted_time >= :startDate${i}::timestamptz AND log_checkpoint.original_submitted_time <= :endDate${i}::timestamptz`,
              {
                [`startDate${i}`]:
                  rangeDateAndTimeDaily[i].utc.startDate + '+00',
                [`endDate${i}`]: rangeDateAndTimeDaily[i].utc.endDate + '+00',
              },
            );
          }
        }),
      );

    // Apply branch filter if provided
    if (branchId) {
      subquery.andWhere('log_checkpoint.branch_id = :branchId', { branchId });
    }

    // Apply zone filters (direct ID or by labels)
    if (zoneId) {
      subquery.andWhere('log_checkpoint.zone_id = :zoneId', { zoneId });
    } else if (zoneLabels) {
      subquery
        .leftJoin('zone_labels', 'zl', 'zl.zone_id = log_checkpoint.zone_id')
        .andWhere('zl.label_id IN (:...zoneLabels)', {
          zoneLabels,
        });
    }

    // Apply user filters (direct ID or by labels)
    if (userId) {
      subquery.andWhere('log_checkpoint.user_id = :userId', { userId });
    } else if (userLabels) {
      subquery
        .leftJoin('user_labels', 'ul', 'ul.user_id = log_checkpoint.user_id')
        .andWhere('ul.label_id IN (:...userLabels)', {
          userLabels,
        });
    }

    // Apply device filters (direct ID or by labels)
    if (deviceId) {
      subquery.andWhere('log_checkpoint.device_id = :deviceId', { deviceId });
    } else if (deviceLabels) {
      subquery
        .leftJoin(
          'device_labels',
          'dl',
          'dl.device_id = log_checkpoint.device_id',
        )
        .andWhere('dl.label_id IN (:...deviceLabels)', {
          deviceLabels,
        });
    }

    // Main query: Group by sequence numbers to calculate entry/exit times and duration
    const mainQuery = this.logCheckpointRepository
      .createQueryBuilder()
      .select([
        'MIN(subquery.zone_id) as zone_id',
        'MIN(subquery.zone_name) as zone_name',
        'MIN(subquery.user_id) as user_id',
        'MIN(subquery.user_name) as user_name',
        'MIN(subquery.device_id) as device_id',
        'MIN(subquery.device_name) as device_name',
        'MIN(subquery.branch_id) as branch_id',
        'MIN(subquery.latitude) as latitude',
        'MIN(subquery.longitude) as longitude',
        'MIN(subquery.original_submitted_time) as original_submitted_time',
        'MIN(subquery.original_submitted_time) as entry',
        'MAX(subquery.original_submitted_time) as exit',
        'MIN(branches.branch_name) as branch_name',
        'MIN(branches.branch_code) as branch_code',
        'EXTRACT(EPOCH FROM (MAX(subquery.original_submitted_time) - MIN(subquery.original_submitted_time))) as duration',
      ])
      .from(`(${subquery.getQuery()})`, 'subquery')
      .setParameters(subquery.getParameters())
      // Group by device, zone and sequence to identify discrete zone visits
      .groupBy(
        ['subquery.device_id', 'subquery.zone_id', 'subquery.seqnum'].join(','),
      );

    // Join branch data to get branch details
    mainQuery.leftJoin(Branch, 'branches', 'branches.id = subquery.branch_id');

    // Apply result ordering
    if (orderBy && orderDirection) {
      mainQuery.orderBy(
        `${orderBy === 'duration' ? 'duration' : orderBy}`,
        orderDirection === OrderDirection.ASC ? 'ASC' : 'DESC',
      );
    }

    // Get total count for pagination
    const countQuery = this.logCheckpointRepository
      .createQueryBuilder()
      .select('COUNT(*)')
      .from(`(${mainQuery.getQuery()})`, 'count_query')
      .setParameters(mainQuery.getParameters());

    console.log(countQuery.getQueryAndParameters());

    const total = await countQuery
      .getRawOne()
      .then(result => parseInt(result.count, 10));


    // Apply pagination
    // if (page && limit) {
    //   mainQuery.offset((page - 1) * limit).limit(limit);
    // }

    // Execute the query
    const results = await mainQuery.getRawMany();

    if (results.length === 0) {
      return {
        data: [],
        total: 0,
      };
    }

    // Get Timezones
    const timezones = await this.branchRepository
      .createQueryBuilder('branch')
      .select(['branch.id', 'timezone.id', 'timezone.timezone_name'])
      .leftJoin('branch.timezone', 'timezone')
      .where('branch.id IN (:...branchIds)', {
        branchIds: results.map((item: any) => item.branch_id),
      })
      .getRawMany();

    // Transform raw results to structured data objects
    const data: TimeOnZoneData[] = results.map(item => {
      const timezone = timezones.find(
        (timezone: any) => timezone.branch_id === item.branch_id,
      );

      return {
        zone_id: item.zone_id,
        zone_name: item.zone_name,
        user_id: item.user_id,
        user_name: item.user_name,
        device_id: item.device_id,
        device_name: item.device_name,
        branch_id: item.branch_id,
        branch_name: item.branch_name || null,
        branch_code: item.branch_code || null,
        timezone_id: timezone?.timezone_id,
        timezone_name: timezone?.timezone_timezone_name,
        latitude: item.latitude,
        longitude: item.longitude,
        original_submitted_time: item.original_submitted_time,
        entry: item.entry,
        exit: item.exit,
        duration: parseInt(item.duration, 10),
      };
    });

    // Return paginated data with total count
    return {
      data,
      total,
    };
  }
}
