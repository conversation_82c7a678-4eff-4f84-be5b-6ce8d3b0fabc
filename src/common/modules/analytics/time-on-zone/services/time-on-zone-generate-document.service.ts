import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as ExcelJS from 'exceljs';
import * as PDFDocument from 'pdfkit';
import { Branch } from '../../../database/entities/branch.entity';
import { User } from '../../../database/entities/user.entity';
import { Device } from '../../../database/entities/device.entity';
import { Label } from '../../../database/entities/label.entity';
import { Zone } from '../../../database/entities/zone.entity';
import { Timezone } from '../../../database/entities/timezone.entity';
import { TimeOnZoneData } from '../interfaces/time-on-zone-analytic.interface';

dayjs.extend(timezone);

export interface TimeOnZoneFilters {
  startDate: string | null;
  endDate: string | null;
  startTime: string | null;
  endTime: string | null;
  branch: Branch | null;
  zone: Zone | null;
  zone_labels: Label[];
  user: User | null;
  user_labels: Label[];
  device: Device | null;
  device_labels: Label[];
}

@Injectable()
export class TimeOnZoneGenerateDocumentService {
  private formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours === 0 && minutes === 0) {
      return `${remainingSeconds}s`;
    } else if (hours === 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  }

  constructor() {}

  /**
   * Generates a PDF document from time on zone entries
   * Creates a professional report with cover page and detailed entries
   *
   * @param data - Array of time on zone entries
   * @param filters - Object containing all applied filters
   * @param timezone - Timezone object for date formatting
   * @returns Promise resolving to an object containing the PDF buffer and filename
   */
  async generatePDF(
    data: TimeOnZoneData[],
    filters: TimeOnZoneFilters,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new PDF document
    const doc = new PDFDocument({
      size: 'A4',
      autoFirstPage: false,
      margins: {
        top: 10,
        bottom: 10,
        left: 10,
        right: 10,
      },
      info: {
        Title: 'Time On Activity Report',
        Author: 'UniGuard System',
        Creator: 'UniGuard',
      },
    });

    // Create a buffer to store the PDF
    const buffers: Buffer[] = [];
    doc.on('data', buffers.push.bind(buffers));

    // Helper function to create section headers
    const drawSectionHeader = (text: string, y: number) => {
      doc
        .fontSize(16)
        .font('Helvetica-Bold')
        .fillColor('#1a237e')
        .text(text, 50, y);

      doc
        .moveTo(50, y + 25)
        .lineTo(545, y + 25)
        .lineWidth(1)
        .strokeColor('#1a237e')
        .stroke();
    };

    // Helper function to add field rows with vertical centering
    const addFieldRow = (
      label: string,
      value: string,
      x: number,
      y: number,
      rowHeight: number = 25,
    ) => {
      const labelY = y + (rowHeight - doc.currentLineHeight()) / 2;
      const valueY = y + (rowHeight - doc.currentLineHeight()) / 2;

      doc
        .font('Helvetica-Bold')
        .fontSize(10)
        .fillColor('#546e7a')
        .text(label, x, labelY);

      doc
        .font('Helvetica')
        .fontSize(10)
        .fillColor('#263238')
        .text(value || '-', x + 90, valueY);

      return y + rowHeight;
    };

    // Add cover page
    doc.addPage();

    // Add background
    doc.rect(0, 0, doc.page.width, doc.page.height).fill('#f5f5f5');

    // Add company logo and name
    doc
      .font('Helvetica-Bold')
      .fontSize(18)
      .fillColor('#1a237e')
      .text(
        'UNIGUARD',
        (doc.page.width - doc.widthOfString('UNIGUARD')) / 2,
        100,
      );

    // Add title
    doc.font('Helvetica-Bold').fontSize(18).fillColor('#1a237e');
    const titleWidth = doc.widthOfString('TIME ON ACTIVITY REPORT');
    doc.text('TIME ON ACTIVITY REPORT', (doc.page.width - titleWidth) / 2, 150);

    // Add active filters section
    const metadataY = 200;
    doc
      .font('Helvetica-Bold')
      .fontSize(14)
      .fillColor('#1a237e')
      .text('Active Filters', 50, metadataY);

    doc
      .moveTo(50, metadataY + 25)
      .lineTo(545, metadataY + 25)
      .lineWidth(1)
      .strokeColor('#1a237e')
      .stroke();

    let filterY = metadataY + 30;

    // Display active filters in a clean table format
    const leftFilters = [
      {
        key: 'startDate',
        label: 'Start Date:',
        valueFunc: () =>
          filters.startDate
            ? `${dayjs(filters.startDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'endDate',
        label: 'End Date:',
        valueFunc: () =>
          filters.endDate
            ? `${dayjs(filters.endDate).format('MMMM D, YYYY')}`
            : 'All Dates',
      },
      {
        key: 'branch',
        label: 'Branch:',
        valueFunc: () =>
          filters.branch ? `${filters.branch.branch_name}` : 'All Branches',
      },
      {
        key: 'user',
        label: 'User:',
        valueFunc: () => (filters.user ? `${filters.user.name}` : 'All Users'),
      },
    ];

    const rightFilters = [
      {
        key: 'startTime',
        label: 'Start Time:',
        valueFunc: () =>
          filters.startTime ? `${filters.startTime}` : 'All Times',
      },
      {
        key: 'endTime',
        label: 'End Time:',
        valueFunc: () => (filters.endTime ? `${filters.endTime}` : 'All Times'),
      },
      {
        key: 'zone',
        label: 'Zone:',
        valueFunc: () =>
          filters.zone ? `${filters.zone.zone_name}` : 'All Zones',
      },
      {
        key: 'device',
        label: 'Device:',
        valueFunc: () =>
          filters.device ? `${filters.device.device_name}` : 'All Devices',
      },
    ];

    // Render filters in two columns
    const rowHeight = 25;
    const leftX = 50;
    const rightX = 300;

    const maxRows = Math.max(leftFilters.length, rightFilters.length);
    for (let i = 0; i < maxRows; i++) {
      // Add left column filter
      if (i < leftFilters.length) {
        const leftFilter = leftFilters[i];
        addFieldRow(
          leftFilter.label,
          leftFilter.valueFunc(),
          leftX,
          filterY,
          rowHeight,
        );
      }

      // Add right column filter
      if (i < rightFilters.length) {
        const rightFilter = rightFilters[i];
        addFieldRow(
          rightFilter.label,
          rightFilter.valueFunc(),
          rightX,
          filterY,
          rowHeight,
        );
      }

      filterY += rowHeight;
    }

    // Add labels section if any labels are applied
    if (
      filters.zone_labels?.length ||
      filters.user_labels?.length ||
      filters.device_labels?.length
    ) {
      filterY += 20;
      doc
        .font('Helvetica-Bold')
        .fontSize(14)
        .fillColor('#1a237e')
        .text('Applied Labels', 50, filterY);

      doc
        .moveTo(50, filterY + 25)
        .lineTo(545, filterY + 25)
        .lineWidth(1)
        .strokeColor('#1a237e')
        .stroke();

      filterY += 30;

      // Helper function to add labels
      const addLabels = (labelType: string, labels: Label[]) => {
        if (labels?.length) {
          filterY = addFieldRow(
            `${labelType} Labels:`,
            labels.map(label => label.label_name).join(', '),
            50,
            filterY,
            25,
          );
        }
      };

      // Add all label types
      addLabels('Zone', filters.zone_labels);
      addLabels('User', filters.user_labels);
      addLabels('Device', filters.device_labels);
    }

    // Add generation info
    filterY = addFieldRow(
      'Report Generated:',
      dayjs().tz(timezone.timezone_name).format('MMMM D, YYYY HH:mm'),
      50,
      filterY + 20,
    );
    filterY = addFieldRow(
      'Total Entries:',
      data.length.toString(),
      50,
      filterY,
    );

    // Add entries
    data.forEach((entry, index) => {
      // Add new page for each entry (3 entries per page)
      if (index % 3 === 0) {
        doc.addPage();
      }

      const yOffset = (index % 3) * 250;
      drawSectionHeader(`Activity Entry #${index + 1}`, 50 + yOffset);

      let entryY = 100 + yOffset;
      const addEntryField = (label: string, value: string) => {
        entryY = addFieldRow(label, value, 50, entryY);
      };

      // Create dayjs object with timezone for reuse
      const originalSubmittedTime = dayjs(entry.original_submitted_time).tz(
        entry.timezone_name,
      );

      addEntryField('Zone:', entry.zone_name);
      addEntryField('User:', entry.user_name);
      addEntryField('Device:', entry.device_name);
      addEntryField(
        'Entry Time:',
        dayjs(entry.entry).format('YYYY-MM-DD HH:mm'),
      );
      addEntryField(
        'Exit Time:',
        dayjs(entry.exit).format('YYYY-MM-DD HH:mm'),
      );
      addEntryField(
        'Original Time:',
        originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
      );
      addEntryField('Duration:', this.formatDuration(entry.duration));
    });

    // Add footer
    const footerText = 'UniGuard Security System';
    const footerWidth = doc.widthOfString(footerText);
    doc
      .fontSize(10)
      .fillColor('#9e9e9e')
      .text(
        footerText,
        (doc.page.width - footerWidth) / 2,
        doc.page.height - 50,
      );

    // Finalize the PDF
    doc.end();

    return new Promise(resolve => {
      doc.on('end', () => {
        const buffer = Buffer.concat(buffers);
        resolve({
          buffer: buffer,
          filename: `time-on-activity-logs-${dayjs().format('YYYYMMDD-HHmmss')}.pdf`,
        });
      });
    });
  }

  /**
   * Generates a spreadsheet document from time on zone entries
   * Creates a professional report with detailed information in Excel format
   *
   * @param data - Array of time on zone entries
   * @param filters - Object containing all applied filters
   * @param timezone - Timezone object for date formatting
   * @returns Promise resolving to an object containing the spreadsheet buffer and filename
   */
  async generateSpreadsheet(
    data: TimeOnZoneData[],
    filters: TimeOnZoneFilters,
    timezone: Timezone,
  ): Promise<{ buffer: Buffer; filename: string }> {
    // Create a new workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Time On Activity Logs');
    let currentRow = 1;

    // Add title and styling (merged across all columns)
    worksheet.mergeCells('A1:G1');
    const titleCell = worksheet.getCell('A1');
    titleCell.value = 'UNIGUARD TIME ON ACTIVITY REPORT';
    titleCell.font = { size: 16, bold: true, color: { argb: '1A237E' } };
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' };

    // Add generation time and total entries
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:G${currentRow}`);
    const timeCell = worksheet.getCell(`A${currentRow}`);
    timeCell.value = `Generated: ${dayjs().tz(timezone.timezone_name).format('YYYY-MM-DD HH:mm')} | Total Entries: ${data.length}`;
    timeCell.font = { size: 10, color: { argb: '546E7A' } };
    timeCell.alignment = { horizontal: 'center' };

    // Add filter information
    currentRow++;
    worksheet.mergeCells(`A${currentRow}:G${currentRow}`);
    const filterTitleCell = worksheet.getCell(`A${currentRow}`);
    filterTitleCell.value = 'FILTER CRITERIA';
    filterTitleCell.font = { size: 12, bold: true, color: { argb: '1A237E' } };

    // Add filter details
    if (filters.startDate && filters.endDate) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Date Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startDate} to ${filters.endDate}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.startTime && filters.endTime) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Time Range:';
      worksheet.getCell(`B${currentRow}`).value =
        `${filters.startTime} to ${filters.endTime}`;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.branch) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Branch:';
      worksheet.getCell(`B${currentRow}`).value = filters.branch.branch_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.zone) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Zone:';
      worksheet.getCell(`B${currentRow}`).value = filters.zone.zone_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.user) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'User:';
      worksheet.getCell(`B${currentRow}`).value = filters.user.name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    if (filters.device) {
      currentRow++;
      worksheet.getCell(`A${currentRow}`).value = 'Device:';
      worksheet.getCell(`B${currentRow}`).value = filters.device.device_name;
      worksheet.getCell(`A${currentRow}`).font = { bold: true };
    }

    // Add labels section if any labels are present
    const hasLabels =
      filters.zone_labels?.length ||
      filters.user_labels?.length ||
      filters.device_labels?.length;

    if (hasLabels) {
      currentRow += 2;
      worksheet.mergeCells(`A${currentRow}:G${currentRow}`);
      const labelsTitleCell = worksheet.getCell(`A${currentRow}`);
      labelsTitleCell.value = 'APPLIED LABELS';
      labelsTitleCell.font = {
        size: 12,
        bold: true,
        color: { argb: '1A237E' },
      };

      // Helper function to add labels
      const addLabels = (labelType: string, labels: Label[]) => {
        if (labels?.length) {
          currentRow++;
          worksheet.getCell(`A${currentRow}`).value = `${labelType} Labels:`;
          worksheet.getCell(`B${currentRow}`).value = labels
            .map(label => label.label_name)
            .join(', ');
          worksheet.getCell(`A${currentRow}`).font = { bold: true };
        }
      };

      // Add all label types
      addLabels('Zone', filters.zone_labels);
      addLabels('User', filters.user_labels);
      addLabels('Device', filters.device_labels);
    }

    // Add date range explanation section
    if (filters.startDate && filters.endDate) {
      currentRow += 2;
      let startRow = currentRow;
      let endRow = currentRow;
      const cellDateRange = worksheet.getCell(`A${currentRow}`);
      cellDateRange.value = 'Date Range:';
      cellDateRange.font = { bold: true };
      cellDateRange.alignment = { horizontal: 'left', vertical: 'middle' };

      // Calculate the number of days between the start and end date
      const startDate = dayjs(filters.startDate);
      const endDate = dayjs(filters.endDate);
      const daysDiff = endDate.diff(startDate, 'day') + 1;
      const startTime = filters.startTime || '00:00';
      const endTime = filters.endTime || '23:59';

      const maxDaysToShow = 3;
      const daysToShow = Math.min(daysDiff, maxDaysToShow);

      for (let i = 0; i < daysToShow; i++) {
        if (i > 0) {
          currentRow++;
        }
        endRow = currentRow;

        const currentDate = startDate.add(i, 'day');
        const dateStr = currentDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      if (daysDiff > maxDaysToShow) {
        currentRow++;
        worksheet.getCell(`B${currentRow}`).value = '...';

        // Show last day
        currentRow++;
        endRow = currentRow;
        const dateStr = endDate.format('YYYY-MM-DD');
        worksheet.getCell(`B${currentRow}`).value =
          `${dateStr} ${startTime} until ${dateStr} ${endTime}`;
      }

      // Merge the date range cells
      worksheet.mergeCells(`A${startRow}:A${endRow}`);
    }

    // Add table headers with styling
    currentRow += 2;
    const headers = [
      'Activity Name',
      'User Name',
      'Device Name',
      'Entry Time',
      'Exit Time',
      'Original Time',
      'Duration',
    ];

    const headerRow = worksheet.addRow(headers);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '1A237E' },
    };
    headerRow.alignment = { horizontal: 'center' };

    // Add data rows with alternating colors
    data.forEach((entry, index) => {
      // Create dayjs object with timezone for reuse
      const originalSubmittedTime = dayjs(entry.original_submitted_time).tz(
        entry.timezone_name,
      );

      const row = worksheet.addRow([
        entry.zone_name,
        entry.user_name,
        entry.device_name,
        dayjs(entry.entry).format('YYYY-MM-DD HH:mm'),
        dayjs(entry.exit).format('YYYY-MM-DD HH:mm'),
        originalSubmittedTime.format('YYYY-MM-DD HH:mm'),
        this.formatDuration(entry.duration),
      ]);

      // Apply alternating row colors
      if (index % 2 === 0) {
        row.eachCell(cell => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F5F5F5' },
          };
        });
      }
    });

    // Set column widths
    worksheet.getColumn(1).width = 20; // Activity Name
    worksheet.getColumn(2).width = 20; // User Name
    worksheet.getColumn(3).width = 20; // Device Name
    worksheet.getColumn(4).width = 20; // Entry Time
    worksheet.getColumn(5).width = 20; // Exit Time
    worksheet.getColumn(6).width = 20; // Original Time
    worksheet.getColumn(7).width = 15; // Duration

    // Add footer
    currentRow = worksheet.rowCount + 2;
    const footerRowIndex = currentRow;
    worksheet.mergeCells(`A${footerRowIndex}:G${footerRowIndex}`);
    const footerCell = worksheet.getCell(`A${footerRowIndex}`);
    footerCell.value = 'UniGuard Security System';
    footerCell.font = { size: 10, color: { argb: '9E9E9E' } };
    footerCell.alignment = { horizontal: 'center' };

    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      buffer: buffer as Buffer,
      filename: `time-on-activity-logs-${dayjs().format('YYYYMMDD-HHmmss')}.xlsx`,
    };
  }
}
